<template>
  <div class="submit-page">
    <div class="container-fluid">
      <el-row :gutter="15" class="mt-1">
        <el-col :span="6">
          <div class="card">
            <el-tabs
              v-model="form.analysisType"
              @tab-change="handleAnalysisTypeChange"
            >
              <el-tab-pane label="Biogeography" name="Biogeography">
                <el-form
                  ref="biogeographyFormRef"
                  :model="form"
                  :rules="biogeographyRules"
                  label-width="auto"
                  style="max-width: 600px"
                  label-position="top"
                >
                  <!-- Species Section -->
                  <el-form-item>
                    <template #label>
                      <div class="section-label">
                        <el-icon color="#0080B0" size="14">
                          <Menu />
                        </el-icon>
                        Species
                      </div>
                    </template>
                    <div class="species-section">
                      <!-- Domain Radio Button Group -->
                      <el-form-item label="Domain" class="mb-2">
                        <el-radio-group
                          v-model="form.domain"
                          class="custom-radio-buttons"
                        >
                          <el-radio-button value="A">Archaea</el-radio-button>
                          <el-radio-button value="B">Bacteria</el-radio-button>
                          <el-radio-button value="E"
                            >Eukaryota
                          </el-radio-button>
                          <el-radio-button value="V">Virus</el-radio-button>
                        </el-radio-group>
                      </el-form-item>

                      <!-- Taxonomy Radio Button Group -->
                      <el-form-item label="Taxonomy" class="mb-2">
                        <el-radio-group
                          v-model="form.level"
                          class="custom-radio-buttons"
                        >
                          <el-radio-button value="P">Phylum</el-radio-button>
                          <el-radio-button value="C">Class</el-radio-button>
                          <el-radio-button value="O">Order</el-radio-button>
                          <el-radio-button value="F">Family</el-radio-button>
                          <el-radio-button value="G">Genus</el-radio-button>
                          <el-radio-button value="S">Species</el-radio-button>
                        </el-radio-group>
                      </el-form-item>

                      <!-- Species Name Multi-select -->
                      <el-form-item
                        label="Species Name"
                        class="mb-2"
                        prop="speciesNames"
                      >
                        <el-select
                          v-model="form.speciesNames"
                          v-loadMore="handleSpeciesLoadMore"
                          multiple
                          filterable
                          remote
                          reserve-keyword
                          :teleported="false"
                          placeholder="Select species names"
                          class="w-100"
                          clearable
                          :max-collapse-tags="3"
                          :remote-method="remoteSpeciesSelect"
                          :loading="speciesSelectLoading"
                          @visible-change="handleSpeciesVisibleChange"
                        >
                          <el-option
                            v-for="it in speciesNameOptions"
                            :key="it.value"
                            :label="it.label"
                            :value="it.value"
                          />
                        </el-select>
                        <div class="species-warning">
                          The maximum number of queried species name is 10
                        </div>
                      </el-form-item>
                    </div>
                  </el-form-item>

                  <!-- Data Filter Section -->
                  <el-form-item>
                    <template #label>
                      <div class="section-label">
                        <el-icon color="#0080B0" size="14">
                          <Menu />
                        </el-icon>
                        Data Select
                      </div>
                    </template>
                    <!-- Biogeography Mode -->
                    <div class="data-filter-section">
                      <el-form-item label="Longitude" class="mb-2">
                        <el-slider
                          v-model="form.sliderLongitude"
                          range
                          :max="180.0"
                          :min="-180.0"
                          :step="0.01"
                          placement="right"
                        />
                        <div class="d-flex align-items-center justify-center">
                          <el-input
                            v-model="form.longitudeTo"
                            style="width: 130px"
                          />
                          <span class="mr-05 ml-05">~</span>
                          <el-input
                            v-model="form.longitudeFrom"
                            style="width: 130px"
                          />
                        </div>
                      </el-form-item>

                      <el-form-item label="Latitude" class="mb-2">
                        <el-slider
                          v-model="form.sliderLatitude"
                          range
                          :max="90"
                          :min="-90"
                          :step="0.01"
                          placement="right"
                        />
                        <div class="d-flex align-items-center justify-center">
                          <el-input
                            v-model="form.latitudeTo"
                            style="width: 130px"
                          />
                          <span class="mr-05 ml-05">~</span>
                          <el-input
                            v-model="form.latitudeFrom"
                            style="width: 130px"
                          />
                        </div>
                      </el-form-item>

                      <el-form-item
                        label="Water Body Type"
                        prop="waterBodyType"
                        class="mb-2"
                      >
                        <el-cascader
                          v-model="form.waterBodyType"
                          :popper-append-to-body="false"
                          :options="waterBodyOpt"
                          :props="props"
                          class="w-100"
                          placeholder="Select"
                          @change="handleWaterBodyTypeChange"
                        />
                      </el-form-item>

                      <el-form-item
                        class="mb-2"
                        label="Or Select Data from Cart"
                        prop="selectedGroup"
                      >
                        <el-select
                          v-model="form.selectedGroup"
                          :teleported="false"
                          placeholder="Select a group"
                          clearable
                        >
                          <el-option
                            v-for="it in groupOptions"
                            :key="it.value"
                            :label="it.label"
                            :value="it.value"
                          />
                        </el-select>
                      </el-form-item>
                    </div>
                  </el-form-item>

                  <el-form-item>
                    <el-button
                      type="primary"
                      :icon="Promotion"
                      class="w-100 filter-search mt-1"
                      :loading="submitLoading"
                      @click="submitBiogeography"
                      >Submit
                    </el-button>
                  </el-form-item>
                </el-form>
              </el-tab-pane>
              <el-tab-pane label="Species Diversity" name="Species Diversity">
                <el-form
                  ref="speciesDiversityFormRef"
                  :model="form"
                  label-width="auto"
                  style="max-width: 600px"
                  label-position="top"
                >
                  <!-- Species Section -->
                  <el-form-item>
                    <template #label>
                      <div class="section-label">
                        <el-icon color="#0080B0" size="14">
                          <Menu />
                        </el-icon>
                        Species
                      </div>
                    </template>
                    <div class="species-section">
                      <!-- Domain Radio Button Group -->
                      <el-form-item label="Domain" class="mb-2" prop="domain">
                        <el-radio-group
                          v-model="form.domain"
                          class="custom-radio-buttons"
                        >
                          <el-radio-button value="A">Archaea</el-radio-button>
                          <el-radio-button value="B">Bacteria</el-radio-button>
                          <el-radio-button value="E"
                            >Eukaryota
                          </el-radio-button>
                          <el-radio-button value="V">Virus</el-radio-button>
                        </el-radio-group>
                      </el-form-item>

                      <!-- Taxonomy Radio Button Group -->
                      <el-form-item
                        label="Taxonomy"
                        class="mb-2"
                        prop="taxonomy"
                      >
                        <el-radio-group
                          v-model="form.level"
                          class="custom-radio-buttons"
                        >
                          <el-radio-button value="P">Phylum</el-radio-button>
                          <el-radio-button value="C">Class</el-radio-button>
                          <el-radio-button value="O">Order</el-radio-button>
                          <el-radio-button value="F">Family</el-radio-button>
                          <el-radio-button value="G">Genus</el-radio-button>
                          <el-radio-button value="S">Species</el-radio-button>
                        </el-radio-group>
                      </el-form-item>
                    </div>
                  </el-form-item>

                  <!-- Data Select Section -->
                  <el-form-item>
                    <template #label>
                      <div class="section-label">
                        <el-icon color="#0080B0" size="14">
                          <Menu />
                        </el-icon>
                        Data Select
                      </div>
                    </template>

                    <!-- Species Diversity Mode -->
                    <div class="data-filter-section">
                      <el-form-item prop="type">
                        <el-radio-group v-model="form.type">
                          <el-radio value="From Biota">From Biota</el-radio>
                          <el-radio value="From Cart">From Cart</el-radio>
                        </el-radio-group>
                      </el-form-item>

                      <div
                        v-for="(group, index) in form.diversityGroups"
                        :key="group.id"
                        class="diversity-group mb-3"
                      >
                        <div
                          class="group-header d-flex justify-space-between align-items-center mb-2"
                        >
                          <el-input
                            v-model="group.name"
                            class="group-title-input"
                            size="small"
                            :style="{ width: '120px' }"
                          />
                          <div class="group-actions">
                            <el-button
                              v-if="index === form.diversityGroups.length - 1"
                              type="success"
                              size="small"
                              round
                              :icon="Plus"
                              @click="addGroup"
                            >
                            </el-button>
                            <el-button
                              v-if="form.diversityGroups.length > 1"
                              type="danger"
                              size="small"
                              round
                              :icon="Minus"
                              @click="removeGroup(index)"
                            >
                            </el-button>
                          </div>
                        </div>

                        <div class="group-content">
                          <el-form-item
                            v-if="form.type === 'From Biota'"
                            class="mb-2"
                            label="Water Body Type"
                          >
                            <el-cascader
                              v-model="group.waterBodyType"
                              :popper-append-to-body="false"
                              :options="waterBodyOpt"
                              :props="props"
                              class="w-100"
                              placeholder="Select"
                            />
                          </el-form-item>

                          <el-form-item
                            v-else
                            class="mb-2"
                            label="Select Data from Cart"
                          >
                            <el-select
                              v-model="group.selectedGroup"
                              :teleported="false"
                              placeholder="Select"
                            >
                              <el-option
                                v-for="it in groupOptions"
                                :key="it.value"
                                :label="it.label"
                                :value="it.value"
                              />
                            </el-select>
                          </el-form-item>
                        </div>
                      </div>
                    </div>
                  </el-form-item>

                  <el-form-item>
                    <el-button
                      type="primary"
                      :icon="Promotion"
                      class="w-100 filter-search mt-1"
                      :loading="submitLoading"
                      @click="submitSpeciesDiversity"
                      >Submit
                    </el-button>
                  </el-form-item>
                </el-form>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-col>
        <el-col :span="18">
          <!-- Biogeography 结果显示区域 -->
          <div
            v-show="form.analysisType === 'Biogeography'"
            class="card mb-1 pos-relative"
          >
            <!-- 有结果时显示地图和数据 -->
            <div v-show="hasResults">
              <div class="d-flex justify-space-between align-items-center">
                <h3 class="mb-0 mt-0">
                  <span class="mr-05 font-600"
                    >Global distribution and relativate abundance of</span
                  >

                  <el-select
                    v-model="summarySelect"
                    :teleported="false"
                    filterable
                    placeholder="Select species names"
                    class="mr-1"
                    style="width: 736px"
                    @change="onSummarySelectChange"
                  >
                    <el-option
                      v-for="species in speciesNameList"
                      :key="species"
                      :label="species"
                      :value="species"
                    />
                  </el-select>
                </h3>
              </div>
              <el-divider class="mt-1"></el-divider>

              <div
                class="map-container"
                :class="{ fullscreen: isMapFullscreen }"
              >
                <div
                  id="diversityMap"
                  style="width: 100%; height: 560px; background-color: #fffff5"
                ></div>
                <!-- 全屏切换按钮 -->
                <el-button
                  class="fullscreen-btn"
                  type="primary"
                  :icon="isMapFullscreen ? 'FullScreen' : 'Rank'"
                  circle
                  :title="isMapFullscreen ? '退出全屏' : '全屏显示'"
                  @click="toggleMapFullscreen"
                >
                </el-button>
              </div>
              <div class="chart-card">
                <!-- 检测/选择样本信息 -->
                <div class="sample-info">
                  <span>Detected/Selected Samples:</span>
                  <span class="sample-count"
                    >{{ sampleStatistics.detectedSamples }}/{{
                      sampleStatistics.selectedSamples
                    }}</span
                  >
                </div>

                <!-- 标准化丰度统计 -->
                <div class="sample-info">
                  <div>Normalized Abundance (Min/Median/MAX):</div>
                  <div class="sample-count">
                    {{ sampleStatistics.min }}/{{ sampleStatistics.median }}/{{
                      sampleStatistics.max
                    }}
                  </div>
                </div>

                <!-- 图例圆圈 -->
                <div class="legend-container">
                  <div class="legend-item">
                    <div class="size-label">0.0001%</div>
                    <div class="circle" style="width: 7px; height: 7px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">0.001%</div>
                    <div class="circle" style="width: 11px; height: 11px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">0.01%</div>
                    <div class="circle" style="width: 17px; height: 17px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">0.1%</div>
                    <div class="circle" style="width: 21px; height: 21px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">1%</div>
                    <div class="circle" style="width: 25px; height: 25px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">10%</div>
                    <div class="circle" style="width: 30px; height: 30px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">100%</div>
                    <div class="circle" style="width: 35px; height: 35px"></div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 无结果时显示空状态 -->
            <div v-show="!hasResults">
              <el-empty
                description="Please select parameters from the left panel and submit to view results"
                :image-size="200"
              >
                <template #image>
                  <el-icon size="100" color="#c0c4cc">
                    <DataAnalysis />
                  </el-icon>
                </template>
              </el-empty>
            </div>
          </div>
          <div
            v-show="form.analysisType === 'Species Diversity'"
            class="card mb-1 pos-relative"
          >
            <!-- 有结果时显示图表 -->
            <div v-show="hasResults" class="chart-container">
              <div v-if="form.type === 'From Biota'" class="d-flex gap-30 mb-3">
                <img
                  src="@/assets/images/img_5.png"
                  alt="Chart 1"
                  style="width: 48%"
                />
                <img
                  src="@/assets/images/img_5.png"
                  alt="Chart 1"
                  style="width: 48%"
                />
              </div>
              <!-- 轮播图替换原来的两张图片 -->
              <div v-else class="chart-swiper-container mb-3">
                <swiper
                  :modules="swiperModules"
                  :slides-per-view="1"
                  :space-between="20"
                  :navigation="true"
                  :pagination="{ clickable: true }"
                  :loop="true"
                  class="chart-swiper"
                  @swiper="onChartSwiper"
                >
                  <swiper-slide v-for="(item, index) in groups">
                    <div class="text-center font-18 font-600">
                      Taxonomic Composition of {{ item }}
                      <a
                        :href="`${apiUrl}/diversity/downloadChart?chartNo=3&taskId=${taskId}&group=${item}`"
                        target="_blank"
                      >
                        <el-icon color="#1F77B4" class="ml-05 cursor-pointer">
                          <Download />
                        </el-icon>
                      </a>
                    </div>
                    <div
                      :id="'Treemap-' + item"
                      :key="index"
                      class="swiper-slide-content"
                    ></div>
                  </swiper-slide>
                </swiper>
              </div>
              <div class="text-center font-18 font-600">
                Taxonomic Composition of Selected Samples
                <a
                  :href="`${apiUrl}/diversity/downloadChart?chartNo=1&taskId=${taskId}&domain=${form.domain}&level=${form.level}`"
                  target="_blank"
                >
                  <el-icon color="#1F77B4" class="ml-05 cursor-pointer">
                    <Download />
                  </el-icon>
                </a>
              </div>
              <div
                id="chart01"
                ref="barRef"
                style="width: 100%; height: 450px"
              ></div>
              <div v-show="false" class="mt-2 d-flex">
                <div class="w-100">
                  <div class="text-center font-18 font-600">
                    A barplot of the LDA values distribution, LDA >
                    2,P-value<0.05
                    <a
                      :href="`${apiUrl}/diversity/downloadChart?chartNo=2&taskId=${taskId}&domain=${form.domain}&level=${form.level}`"
                      target="_blank"
                    >
                      <el-icon color="#1F77B4" class="ml-05 cursor-pointer">
                        <Download />
                      </el-icon>
                    </a>
                  </div>
                  <div
                    id="ldaChart"
                    ref="ldaRef"
                    style="width: 100%; height: 500px"
                  ></div>
                </div>
              </div>
              <div class="mt-2 d-flex">
                <div class="w-100">
                  <div class="text-center font-18 font-600">
                    Taxonomic Composition of Selected Samples
                    <a
                      :href="`${apiUrl}/diversity/downloadChart?chartNo=4&taskId=${taskId}&domain=${form.domain}&level=${form.level}&betaMethod=bray`"
                      target="_blank"
                    >
                      <el-icon color="#1F77B4" class="ml-05 cursor-pointer">
                        <Download />
                      </el-icon>
                    </a>
                  </div>
                  <iframe
                    :src="pcoaUrl"
                    frameborder="0"
                    style="width: 100%; height: 500px"
                  ></iframe>
                </div>
              </div>
            </div>
            <!-- 无结果时显示空状态 -->
            <div v-show="!hasResults">
              <el-empty
                description="Please select parameters from the left panel and submit to view results"
                :image-size="200"
              >
                <template #image>
                  <el-icon size="100" color="#c0c4cc">
                    <DataAnalysis />
                  </el-icon>
                </template>
              </el-empty>
            </div>
          </div>
          <div v-show="hasResults" class="card">
            <!-- 有结果时显示表格 -->
            <div>
              <div class="d-flex justify-space-between align-items-center">
                <h3 class="mb-0 mt-0">Sample List</h3>
                <el-popover
                  placement="bottom-end"
                  :width="300"
                  trigger="hover"
                  popper-class="metadata-popover"
                >
                  <template #reference>
                    <el-button type="success"> Add other metadata</el-button>
                  </template>
                  <div class="metadata-selector">
                    <h4 class="metadata-title">Select Columns to Display</h4>
                    <div class="column-checkboxes">
                      <el-checkbox
                        v-for="column in allColumns"
                        :key="column.prop"
                        v-model="column.visible"
                        :label="column.label"
                        class="column-checkbox"
                      />
                    </div>
                  </div>
                </el-popover>
              </div>
              <el-divider class="mt-1"></el-divider>
              <el-table
                ref="table"
                tooltip-effect="dark"
                :data="dataTable"
                :header-cell-style="{
                  backgroundColor: '#F1F5F9',
                  color: '#333333',
                  fontWeight: 700,
                }"
                border
                :stripe="true"
              >
                <!-- Default visible columns -->
                <el-table-column
                  v-if="getColumnVisibility('runId')"
                  label="Run ID"
                  prop="runId"
                  width="200"
                >
                  <template #default="scope">
                    <div class="d-flex">
                      <el-tag
                        v-if="scope.row.group"
                        effect="dark"
                        round
                        :type="getGroupTagType(scope.row.group)"
                        size="small"
                        class="mr-05"
                      >
                        {{ scope.row.group }}
                      </el-tag>
                      {{ scope.row.runId }}
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('bioProjectId')"
                  label="BioProject ID"
                  prop="bioProjectId"
                  width="150"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('latitude')"
                  label="Latitude"
                  prop="latitude"
                  width="120"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('longitude')"
                  label="Longitude"
                  prop="longitude"
                  width="120"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('hydrosphereType')"
                  label="Hydrosphere Type"
                  prop="hydrosphereType"
                  width="160"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('geolocation')"
                  label="Geolocation"
                  prop="waterBodyTypeByGeographic"
                  width="150"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('waterBodyType')"
                  label="Water Body Type"
                  prop="waterBodyTypeByClassification"
                  width="160"
                ></el-table-column>

                <!-- Hidden columns that can be toggled -->
                <el-table-column
                  v-if="getColumnVisibility('depth')"
                  label="Depth"
                  prop="depth"
                  width="100"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('temperature')"
                  label="Temperature"
                  prop="temperature"
                  width="120"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('salinity')"
                  label="Salinity"
                  prop="salinity"
                  width="100"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('ph')"
                  label="pH"
                  prop="ph"
                  width="80"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('criticalZone')"
                  label="Critical Zone"
                  prop="criticalZone"
                  width="120"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('samplingSubstrate')"
                  label="Sampling Substrate"
                  prop="samplingSubstrate"
                  width="150"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('country')"
                  label="Country"
                  prop="country"
                  width="120"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('waterBodyName')"
                  label="Water Body Name"
                  prop="waterBodyName"
                  width="160"
                ></el-table-column>

                <!-- Analysis Results column (always visible) -->
                <el-table-column
                  v-if="getColumnVisibility('analysisResults')"
                  label="Analysis Results"
                  prop="analysisResults"
                  width="160"
                  align="center"
                >
                  <template #default="scope">
                    <router-link :to="`/sample/detail/${scope.row.runId}`">
                      <div class="text-primary">View</div>
                    </router-link>
                  </template>
                </el-table-column>
              </el-table>

              <pagination
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                :total="total"
                class="mb-1"
                :auto-scroll="false"
                @pagination="fetchTableData"
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <BrowseCart />
  </div>
</template>

<script setup>
  import {
    computed,
    getCurrentInstance,
    nextTick,
    onMounted,
    reactive,
    ref,
    toRefs,
    watch,
  } from 'vue';
  import {
    DataAnalysis,
    Menu,
    Minus,
    Plus,
    Promotion,
  } from '@element-plus/icons-vue';
  import L from 'leaflet';
  import 'leaflet/dist/leaflet.css';
  import * as echarts from 'echarts';

  import BrowseCart from '@/components/ShoppingCart/BrowseCart.vue';
  import {
    createBiogeography,
    createSpeciesDiversity,
    getBarPlotData,
    getResultMapData,
    getSpeciesName,
    getSpeciesNameList,
    getTableResult,
    getTreeMapData,
  } from '@/api/diversity';
  import { getWaterBodyTypeByHydrosphere } from '@/api/samples';
  import { useCartStore } from '@/store/modules/cart';
  import axios from 'axios';

  // Swiper imports
  import 'swiper/css';
  import 'swiper/css/navigation';
  import 'swiper/css/pagination';
  import { Swiper, SwiperSlide } from 'swiper/vue';
  import { Navigation } from 'swiper/modules';
  import { countPercentage, debounce } from '@/utils';

  const { proxy } = getCurrentInstance();

  const cartStore = useCartStore();

  // 表单引用
  const biogeographyFormRef = ref(null);
  const speciesDiversityFormRef = ref(null);
  const apiUrl = ref(import.meta.env.VITE_APP_BASE_API);

  const form = ref({
    analysisType: 'Biogeography',
    domain: 'A',
    level: 'P',
    speciesNames: [],
    sliderLongitude: [-180.0, 180.0],
    sliderLatitude: [-90, 90],
    longitudeTo: -180.0,
    longitudeFrom: 180.0,
    latitudeTo: -90,
    latitudeFrom: 90,
    waterBodyType: [],
    geolocation: '',
    type: 'From Cart',
    selectedGroup: '', // 改为单选，存储选中的group名称
    diversityGroups: [
      {
        id: 1,
        name: 'GroupA',
        waterBodyType: [],
        selectedGroup: '',
      },
    ],
  });

  // biogeography 相关数据
  const biogeographyData = reactive({
    submitLoading: false,
    taskId: 'demo',
    speciesNameList: [], // 地图下拉框选项
    summarySelect: '', // 当前选中的物种
    hasResults: false, // 是否有结果数据
    sampleStatistics: {
      detectedSamples: 0,
      selectedSamples: 0,
      min: 0,
      median: 0,
      max: 0,
    },
  });

  // 解构 biogeographyData 中的响应式属性
  const { submitLoading, taskId, speciesNameList, summarySelect, hasResults } =
    toRefs(biogeographyData);

  const speciesNameOptions = ref([]);

  // 物种名称分页查询参数
  const speciesSelectQueryParams = ref({
    search: '',
    pageNum: 0,
    pageSize: 200,
  });

  // 物种名称选择器加载状态
  const speciesSelectLoading = ref(false);

  // 从购物车store获取分组选项
  const groupOptions = computed(() => {
    return cartStore.cartGroups.map(group => ({
      label: `${group.name} (${group.runCount} runs)`,
      value: group.name,
    }));
  });
  const table = ref(null);

  // 查询参数和分页
  const queryParams = ref({
    pageNum: 1,
    pageSize: 20,
  });
  const total = ref(0);

  // 购物车选择相关
  const selectedCartCount = ref(0);
  const props = {
    expandTrigger: 'hover',
  };

  // Swiper 相关
  const swiperModules = ref([Navigation]);
  const chartSwiper = ref(null);

  // TreeMap 初始化状态
  const treeMapInitialized = ref(false);

  const onChartSwiper = swiper => {
    chartSwiper.value = swiper;
  };

  const barRef = ref(null);
  let barInstance = null;

  const ldaRef = ref(null);
  let ldaInstance = null;

  const isMapFullscreen = ref(false);

  // 样本统计信息
  const sampleStatistics = ref({
    detectedSamples: 0,
    selectedSamples: 0,
    min: '',
    max: '',
    median: '',
  });

  // 当前数据层引用
  let currentDataLayer = null;

  // 地理数据
  const oceanData = ref(null);
  const lakesData = ref(null);
  const riversData = ref(null);
  const geoDataLoading = ref(false);

  // 动态加载的水体类型选项，参考genomic页面实现
  const waterBodyOpt = ref([
    {
      value: 'water_body_type_by_geographic',
      label: 'Geolocation',
      children: [],
    },
    {
      value: 'water_body_type_by_classification',
      label: 'Water Body Type',
      children: [],
    },
  ]);

  // 监听滑动条变化，同步到输入框
  watch(
    () => form.value.sliderLongitude,
    newVal => {
      form.value.longitudeTo = newVal[0];
      form.value.longitudeFrom = newVal[1];
    },
  );

  watch(
    () => form.value.sliderLatitude,
    newVal => {
      form.value.latitudeTo = newVal[0];
      form.value.latitudeFrom = newVal[1];
    },
  );

  // 监听输入框变化，同步到滑动条
  watch(
    () => [form.value.longitudeTo, form.value.longitudeFrom],
    newVal => {
      form.value.sliderLongitude = [newVal[0], newVal[1]];
    },
  );

  watch(
    () => [form.value.latitudeTo, form.value.latitudeFrom],
    newVal => {
      form.value.sliderLatitude = [newVal[0], newVal[1]];
    },
  );

  // 表单验证规则
  const biogeographyRules = ref({
    speciesNames: [
      {
        required: true,
        message: 'Please select at least one species',
        trigger: 'change',
      },
    ],
    // 自定义验证：Water Body Type 和 Selected Group 必须选择其中一个
    waterBodyType: [{ validator: validateDataSelection, trigger: 'change' }],
    selectedGroup: [{ validator: validateDataSelection, trigger: 'change' }],
  });

  // 自定义验证函数：确保 Water Body Type 或 Selected Group 至少选择一个
  function validateDataSelection(rule, value, callback) {
    if (!form.value.waterBodyType || form.value.waterBodyType.length === 0) {
      if (!form.value.selectedGroup) {
        callback(
          new Error(
            'Please select either Water Body Type or a Group from Cart',
          ),
        );
        return;
      }
    }
    callback();
  }

  // 监听selectedGroup变化，提供用户反馈
  watch(
    () => form.value.selectedGroup,
    (newVal, oldVal) => {
      if (newVal && !oldVal) {
        // 选中了group
        console.log(`Selected group: ${newVal}`);
      } else if (!newVal && oldVal) {
        // 清除了group选择
        console.log('Cleared group selection, will use filter conditions');
      }
    },
  );

  // 监听domain和taxonomy变化，获取物种名称选项
  watch(
    () => [form.value.domain, form.value.level],
    ([domain, level]) => {
      if (domain && level) {
        form.value.speciesNames = [];
        // 清空现有选项，等待用户打开选择器时再加载
        speciesNameOptions.value = [];
        speciesSelectQueryParams.value.pageNum = 0;
        speciesSelectQueryParams.value.search = '';
      }
    },
    { immediate: true },
  );

  // 监听hasResults变化，当显示结果时重新渲染地图
  watch(
    () => hasResults.value,
    newVal => {
      if (newVal) {
        nextTick(() => {
          setTimeout(() => {
            if (window.mapInstance) {
              window.mapInstance.invalidateSize();
            }
          }, 100);
        });
      }
    },
  );

  // 全屏切换方法
  function toggleMapFullscreen() {
    isMapFullscreen.value = !isMapFullscreen.value;
    nextTick(() => {
      setTimeout(() => {
        const mapElement = document.getElementById('diversityMap');
        if (mapElement && window.mapInstance) {
          window.mapInstance.invalidateSize();
        }
      }, 300);
    });
  }

  // 防抖的远程搜索物种名称
  const debouncedRemoteSpeciesSelect = debounce(() => {
    if (!form.value.domain || !form.value.level) {
      return;
    }

    getSpeciesName({
      domain: form.value.domain,
      level: form.value.level,
      search: speciesSelectQueryParams.value.search,
      pageNum: speciesSelectQueryParams.value.pageNum,
      pageSize: speciesSelectQueryParams.value.pageSize,
    })
      .then(response => {
        if (speciesSelectQueryParams.value.pageNum === 0) {
          speciesNameOptions.value = [];
        }
        speciesNameOptions.value.push(...response.data);
      })
      .catch(error => {
        console.error('获取物种名称选项失败:', error);
      })
      .finally(() => {
        speciesSelectLoading.value = false;
      });
  }, 300);

  // 远程搜索物种名称
  function remoteSpeciesSelect(queryString) {
    if (!form.value.domain || !form.value.level) {
      return;
    }

    speciesSelectLoading.value = true;
    speciesSelectQueryParams.value.search = queryString || '';

    // 如果是搜索（不是分页加载），重置页码
    if (queryString !== undefined) {
      speciesSelectQueryParams.value.pageNum = 0;
    }

    debouncedRemoteSpeciesSelect();
  }

  // 处理物种名称选择器加载更多
  function handleSpeciesLoadMore() {
    speciesSelectQueryParams.value.pageNum += 1;
    remoteSpeciesSelect(speciesSelectQueryParams.value.search);
  }

  // 处理物种名称选择器显示/隐藏
  function handleSpeciesVisibleChange(visible) {
    if(!visible){
      speciesSelectQueryParams.value.pageNum = 0;
      speciesNameOptions.value = []
    }
  }

  // 处理级联选择器变化
  function handleWaterBodyTypeChange(value) {
    console.log('Water body type changed:', value);
    if (value && value.length > 0) {
      const category = value[0]; // 一级选择
      // 当用户选择一级选项时，加载对应的二级选项
      loadSecondLevelOptions(category);
    }
  }

  // 加载水体类型级联选择器数据
  function loadWaterBodyTypeOptions() {
    console.log('Loading water body type options...');
    // 初始化时加载所有分类的二级选项
    loadSecondLevelOptions('water_body_type_by_geographic');
    loadSecondLevelOptions('water_body_type_by_classification');
  }

  // 加载指定分类的二级选项
  function loadSecondLevelOptions(category) {
    console.log('Loading second level options for category:', category);

    getWaterBodyTypeByHydrosphere({
      hydrosphereType: 'All',
      category: category,
    })
      .then(response => {
        console.log(`${category} response:`, response);
        if (response.data && response.data.length) {
          const options = response.data.map(item => ({
            label: item,
            value: item,
          }));

          // 找到对应的一级选项并更新其children
          const targetIndex =
            category === 'water_body_type_by_geographic' ? 0 : 1;
          waterBodyOpt.value[targetIndex].children = options;
          console.log(`Updated ${category} options:`, options);
        } else {
          console.log(`No ${category} data received`);
        }
      })
      .catch(error => {
        console.error(`Failed to load ${category} options:`, error);
      });
  }

  // 提交biogeography任务
  function submitBiogeography() {
    // 表单验证
    if (!biogeographyFormRef.value) return;

    biogeographyFormRef.value.validate(valid => {
      if (!valid) {
        return;
      }

      // 继续原有的验证逻辑
      if (form.value.speciesNames.length === 0) {
        proxy.$modal.msgError('Please select at least one species name');
        return;
      }

      if (form.value.speciesNames.length > 10) {
        proxy.$modal.msgError('Maximum 10 species names allowed');
        return;
      }

      // 执行提交逻辑
      performSubmit();
    });
  }

  // 实际的提交逻辑
  function performSubmit() {
    // 开始提交
    submitLoading.value = true;

    // 显示全局遮罩
    proxy.$modal.loading('Submitting analysis request...');

    // 构建提交参数
    const params = {
      domain: form.value.domain,
      level: form.value.level,
      speciesNames: form.value.speciesNames,
    };

    // 根据是否选中group来决定提交的数据
    if (form.value.selectedGroup) {
      // 如果选中了group，提交group的runIds
      const selectedGroupData = cartStore.cartGroups.find(
        group => group.name === form.value.selectedGroup,
      );
      if (selectedGroupData) {
        params.runIds = selectedGroupData.runIds;
      } else {
        proxy.$modal.closeLoading();
        proxy.$modal.msgError('Selected group not found in cart');
        submitLoading.value = false;
        return;
      }
    } else {
      // 如果没有选中group，使用筛选条件
      params.runIds = [];
      params.queryDTO = {
        latitudeStart: form.value.latitudeTo,
        latitudeEnd: form.value.latitudeFrom,
        longitudeStart: form.value.longitudeTo,
        longitudeEnd: form.value.longitudeFrom,
      };

      // 根据一级下拉框的选择决定提交字段
      if (
        Array.isArray(form.value.waterBodyType) &&
        form.value.waterBodyType.length >= 2
      ) {
        const firstLevel = form.value.waterBodyType[0];
        const secondLevel = form.value.waterBodyType[1];

        if (firstLevel === 'water_body_type_by_geographic') {
          params.queryDTO.waterBodyTypeByGeographic = secondLevel;
        } else if (firstLevel === 'water_body_type_by_classification') {
          params.queryDTO.waterBodyTypeByClassification = secondLevel;
        }
      }
    }

    createBiogeography(params)
      .then(response => {
        taskId.value = response.msg;
        // 任务提交成功后，获取物种名称列表
        fetchSpeciesNameList();
        proxy.$modal.msgSuccess('Analysis submitted successfully');
      })
      .catch(error => {
        console.error('提交任务失败:', error);
        proxy.$modal.msgError('Failed to submit analysis');
      })
      .finally(() => {
        proxy.$modal.closeLoading();
        submitLoading.value = false;
      });
  }

  // 获取物种名称列表（用于地图下拉框）
  function fetchSpeciesNameList() {
    if (!taskId.value) return;

    getSpeciesNameList({ taskId: taskId.value })
      .then(response => {
        speciesNameList.value = response.data || [];

        // 默认选中第一个
        if (speciesNameList.value.length > 0) {
          summarySelect.value = speciesNameList.value[0];
          // 设置有结果状态
          hasResults.value = true;
          onSummarySelectChange();
        }
      })
      .catch(error => {
        console.error('获取物种名称列表失败:', error);
      });
  }

  // 结果下拉框选择变化时的处理
  function onSummarySelectChange() {
    console.log(taskId.value, summarySelect.value);
    if (summarySelect.value && taskId.value) {
      // 重置分页到第一页
      queryParams.value.pageNum = 1;
      fetchMapData();
      fetchTableData();
    }
  }

  // 获取地图数据
  function fetchMapData() {
    if (!taskId.value || !summarySelect.value) return;

    getResultMapData({
      taskId: taskId.value,
      speciesName: summarySelect.value,
    })
      .then(response => {
        const result = response.data;
        // 检查是否返回 null（没有分析结果）
        if (result) {
          // 正常处理数据
          const data = result || {};
          updateMapWithData(data.mapData || []);
          updateSampleStatistics(data);
        } else {
          proxy.$modal.confirm(`Taxonomy unknown: ${summarySelect.value}`);
          // 显示空地图和空统计信息
          updateMapWithData([]);
          updateSampleStatistics({});
        }
      })
      .catch(error => {
        console.error('获取地图数据失败:', error);
      });
  }

  // 获取表格数据
  function fetchTableData() {
    // Species Diversity 模式下不需要 summarySelect
    if (form.value.analysisType === 'Species Diversity') {
      if (!taskId.value) return;
    } else {
      if (!taskId.value || !summarySelect.value) return;
    }

    const params = {
      taskId: taskId.value,
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
    };

    // 只有 Biogeography 模式才传 speciesName
    if (form.value.analysisType === 'Biogeography' && summarySelect.value) {
      params.speciesName = summarySelect.value;
    }

    getTableResult(params)
      .then(response => {
        console.log(response);

        // 使用正确的数据结构
        dataTable.value = response.rows || [];
        total.value = response.total || 0;
      })
      .catch(error => {
        console.error('获取表格数据失败:', error);
      });
  }

  // 更新地图数据
  function updateMapWithData(mapData) {
    if (!window.mapInstance || !mapData || mapData.length === 0) {
      return;
    }

    // 清除之前的数据层
    if (currentDataLayer) {
      window.mapInstance.removeLayer(currentDataLayer);
    }

    // 创建canvas渲染器
    const canvasRenderer = L.canvas({ padding: 0.5 });

    // 创建图层组
    const pointsLayer = L.layerGroup();

    mapData.forEach(item => {
      if (item.latitude && item.longitude && item.number > 0) {
        // 根据value值计算圆点大小，使用提供的公式
        const value = item.size || item.number; // 使用size字段，如果没有则使用number
        const radius = Math.log10(value * 8) * 2.1;
        const finalRadius = Math.max(3, Math.min(35, radius)); // 限制最小和最大半径

        const circleMarker = L.circleMarker([item.latitude, item.longitude], {
          radius: finalRadius,
          fillColor: '#C6A5F4',
          color: '#C6A5F4',
          opacity: 1,
          weight: 0.5,
          fillOpacity: 1,
          pane: 'pointsPane',
          renderer: canvasRenderer,
        });

        // 添加弹出框显示详细信息
        const formattedLatitude = formatNumber(item.latitude);
        const formattedLongitude = formatNumber(item.longitude);
        const formattedMaxAbundance = formatNumber(value / 10000);

        circleMarker.bindPopup(`
          <div style="font-family: Arial, sans-serif; line-height: 1.4;">
            <div style="margin-bottom: 4px;">
              <strong>Sample Count:</strong> <span style="color: #E6A23C;">${item.number}</span>
            </div>
            <div style="margin-bottom: 4px;">
              <strong>Latitude:</strong> ${formattedLatitude}°
            </div>
            <div style="margin-bottom: 4px;">
              <strong>Longitude:</strong> ${formattedLongitude}°
            </div>
            <div style="margin-bottom: 4px;">
              <strong>Max Abundance:</strong> ${formattedMaxAbundance}
            </div>
          </div>
        `);

        pointsLayer.addLayer(circleMarker);
      }
    });

    // 添加到地图
    if (pointsLayer.getLayers().length > 0) {
      pointsLayer.addTo(window.mapInstance);
      currentDataLayer = pointsLayer;
    }
  }

  // 数字格式化函数
  function formatNumber(number) {
    const decimalPart = number.toString().split('.')[1];
    if (decimalPart && decimalPart.length > 4) {
      return number.toFixed(4);
    } else {
      return number;
    }
  }

  // 更新样本统计信息
  function updateSampleStatistics(result) {
    sampleStatistics.value.detectedSamples = result.detectedNum || 0;
    sampleStatistics.value.selectedSamples = result.selectedNum || 0;
    sampleStatistics.value.min = formatNumber(parseFloat(result.min || 0));
    sampleStatistics.value.max = formatNumber(parseFloat(result.max || 0));
    sampleStatistics.value.median = formatNumber(
      parseFloat(result.median || 0),
    );
  }

  // 异步获取地理数据的函数
  async function fetchGeoData() {
    try {
      geoDataLoading.value = true;
      // 获取基础路径
      const basePath = import.meta.env.VITE_APP_PUBLIC_PATH || '/';

      const [oceanResponse, lakesResponse, riversResponse] = await Promise.all([
        axios.get(`${basePath}/geojson/ocean.json`),
        axios.get(`${basePath}/geojson/sample_lakes.json`),
        axios.get(`${basePath}/geojson/sample_rivers.json`),
      ]);

      oceanData.value = oceanResponse.data;
      lakesData.value = lakesResponse.data;
      riversData.value = riversResponse.data;
      return true;
    } catch (error) {
      console.error('加载地理数据失败:', error);
      return false;
    } finally {
      geoDataLoading.value = false;
    }
  }

  let groups = ref([]);

  // 提交Species Diversity任务
  function submitSpeciesDiversity() {
    // JavaScript 校验
    if (!form.value.domain) {
      proxy.$modal.msgError('Please select a domain');
      return;
    }

    if (!form.value.level) {
      proxy.$modal.msgError('Please select a taxonomy level');
      return;
    }

    if (!form.value.type) {
      proxy.$modal.msgError('Please select data source type');
      return;
    }

    // 验证分组配置
    if (
      !form.value.diversityGroups ||
      form.value.diversityGroups.length === 0
    ) {
      proxy.$modal.msgError('Please add at least one group');
      return;
    }

    for (let i = 0; i < form.value.diversityGroups.length; i++) {
      const group = form.value.diversityGroups[i];

      if (!group.name || group.name.trim() === '') {
        proxy.$modal.msgError(`Please enter a name for Group ${i + 1}`);
        return;
      }

      if (form.value.type === 'From Biota') {
        if (!group.waterBodyType || group.waterBodyType.length === 0) {
          proxy.$modal.msgError(
            `Please select Water Body Type for ${group.name}`,
          );
          return;
        }
      } else if (form.value.type === 'From Cart') {
        if (!group.selectedGroup) {
          proxy.$modal.msgError(
            `Please select a group from cart for ${group.name}`,
          );
          return;
        }
      }
    }

    // 开始提交
    submitLoading.value = true;
    proxy.$modal.loading('Submitting Species Diversity analysis request...');

    // 构建提交参数
    const params = {
      domain: form.value.domain,
      level: form.value.level,
      groupInfos: [],
    };

    // 构建分组信息
    form.value.diversityGroups.forEach(group => {
      const groupInfo = {
        groupName: group.name.trim(),
      };

      if (form.value.type === 'From Biota') {
        // 根据级联选择器的值设置对应字段
        if (group.waterBodyType && group.waterBodyType.length >= 2) {
          const firstLevel = group.waterBodyType[0];
          const secondLevel = group.waterBodyType[1];

          if (firstLevel === 'water_body_type_by_geographic') {
            groupInfo.waterBodyTypeByGeographic = secondLevel;
          } else if (firstLevel === 'water_body_type_by_classification') {
            groupInfo.waterBodyTypeByClassification = secondLevel;
          }
        }
      } else if (form.value.type === 'From Cart') {
        // 从购物车获取runIds
        const selectedGroupData = cartStore.cartGroups.find(
          cartGroup => cartGroup.name === group.selectedGroup,
        );
        if (selectedGroupData) {
          groupInfo.runIds = selectedGroupData.runIds;
        }
      }

      params.groupInfos.push(groupInfo);
    });

    console.log('Submitting Species Diversity params:', params);

    createSpeciesDiversity(params)
      .then(response => {
        groups.value = params.groupInfos.map(x => x.groupName);
        taskId.value = response.msg;
        // 设置有结果状态，触发结果展示
        hasResults.value = true;
        // 初始化图表
        initSpeciesDiversityCharts();
        proxy.$modal.msgSuccess(
          'Species Diversity analysis submitted successfully',
        );
        console.log('Species Diversity task ID:', response.msg);
      })
      .catch(error => {
        console.error('提交Species Diversity任务失败:', error);
        proxy.$modal.msgError('Failed to submit Species Diversity analysis');
      })
      .finally(() => {
        proxy.$modal.closeLoading();
        submitLoading.value = false;
      });
  }

  // 添加分组
  function addGroup() {
    const newGroupId = form.value.diversityGroups.length + 1;
    const groupLetter = String.fromCharCode(64 + newGroupId); // A, B, C, etc.

    form.value.diversityGroups.push({
      id: newGroupId,
      name: `Group${groupLetter}`,
      waterBodyType: [],
      selectedGroup: '',
    });
  }

  // 移除分组
  function removeGroup(index) {
    if (form.value.diversityGroups.length > 1) {
      form.value.diversityGroups.splice(index, 1);
    }
  }

  const dataTable = ref([]);

  // 显示隐藏列
  const allColumns = ref([
    { prop: 'runId', label: 'Run ID', visible: true, default: true },
    {
      prop: 'bioProjectId',
      label: 'BioProject ID',
      visible: true,
      default: true,
    },
    { prop: 'latitude', label: 'Latitude', visible: true, default: true },
    { prop: 'longitude', label: 'Longitude', visible: true, default: true },
    {
      prop: 'hydrosphereType',
      label: 'Hydrosphere Type',
      visible: true,
      default: true,
    },
    { prop: 'geolocation', label: 'Geolocation', visible: true, default: true },
    {
      prop: 'waterBodyType',
      label: 'Water Body Type',
      visible: true,
      default: true,
    },
    {
      prop: 'analysisResults',
      label: 'Analysis Results',
      visible: true,
      default: true,
    },
    { prop: 'depth', label: 'Depth', visible: false, default: false },
    {
      prop: 'temperature',
      label: 'Temperature',
      visible: false,
      default: false,
    },
    { prop: 'salinity', label: 'Salinity', visible: false, default: false },
    { prop: 'ph', label: 'pH', visible: false, default: false },
    {
      prop: 'criticalZone',
      label: 'Critical Zone',
      visible: false,
      default: false,
    },
    {
      prop: 'samplingSubstrate',
      label: 'Sampling Substrate',
      visible: false,
      default: false,
    },
    { prop: 'country', label: 'Country', visible: false, default: false },
    {
      prop: 'waterBodyName',
      label: 'Water Body Name',
      visible: false,
      default: false,
    },
  ]);

  function getColumnVisibility(prop) {
    const column = allColumns.value.find(col => col.prop === prop);
    return column ? column.visible : false;
  }

  // 获取分组标签类型
  function getGroupTagType(group) {
    const groupTypes = {
      'Group A': 'primary',
      'Group B': 'warning',
      'Group C': 'success',
      'Group D': 'info',
      'Group E': 'danger',
    };
    return groupTypes[group] || 'info';
  }

  const initMap = id => {
    // 检查所有地理数据是否已加载
    if (!oceanData.value || !lakesData.value || !riversData.value) {
      console.warn('地理数据尚未加载完成，等待加载...');
      return null;
    }

    var latlng = L.latLng(30, 0);

    var map = L.map(id, {
      center: latlng,
      zoom: 2,
      minZoom: 2,
      maxZoom: 18,
      zoomControl: false,
      attributionControl: false,
      maxBounds: [
        [-90, -180],
        [90, 180],
      ],
    });

    map.createPane('oceanPane');
    map.createPane('riverPane');
    map.createPane('pointsPane');

    map.getPane('oceanPane').style.zIndex = 300; // 海洋图层
    map.getPane('riverPane').style.zIndex = 400; // 河流图层
    map.getPane('pointsPane').style.zIndex = 500; // 圆点图层

    const canvasRenderer = L.canvas({ padding: 0.5 });

    L.geoJSON(oceanData.value, {
      onEachFeature: function (feature, layer) {
        let labelLatLng;
        // 根据特征名称选择标签位置
        if (feature.properties.name === 'North Pacific Ocean') {
          labelLatLng = L.latLng(30, -150);
        } else if (feature.properties.name === 'South Pacific Ocean') {
          labelLatLng = L.latLng(-30, -140);
        } else {
          // 默认使用中心点
          labelLatLng = layer.getBounds().getCenter();
        }

        // 创建一个标记
        var label = L.marker(labelLatLng, {
          icon: L.divIcon({
            className: 'ocean-label',
            html: feature.properties.name,
            iconSize: [100, 20],
          }),
        });
        label.addTo(map); // 将标签添加到地图
      },

      style: function () {
        return {
          fillColor: '#1C4F80', // 设置填充颜色为蓝色
          weight: 1,
          opacity: 1, // 不透明度设置为 1
          color: 'rgba(0, 0, 0, 0)', // 边界颜色设置为透明
          fillOpacity: 1, // 填充不透明度
          pane: 'oceanPane',
          renderer: canvasRenderer, // 使用 Canvas 渲染
        };
      },
    }).addTo(map);

    // var lakeLayer = null;
    L.geoJSON(lakesData.value, {
      onEachFeature: function (feature, layer) {
        // 创建一个标记
        var label = L.marker(layer.getBounds().getCenter(), {
          icon: L.divIcon({
            iconSize: [100, 20],
          }),
        });
        label.addTo(map); // 将标签添加到地图
        map.on('zoomend', function () {
          let zoom = map.getZoom();
          label.setIcon(
            L.divIcon({
              className: 'lake-label',
              html: zoom > 4 ? feature.properties.Name : '',
            }),
          );
        });
      },

      style: function () {
        return {
          fillColor: '#9ABAE7', // 设置填充颜色为蓝色
          weight: 1,
          opacity: 1, // 不透明度设置为 1
          color: 'rgba(0, 0, 0, 0)', // 边界颜色设置为透明
          fillOpacity: 1, // 填充不透明度
          pane: 'oceanPane',
          renderer: canvasRenderer, // 使用 Canvas 渲染
        };
      },
    }).addTo(map);

    L.geoJSON(riversData.value, {
      onEachFeature: function (feature, layer) {
        // 创建一个标记
        var label = L.marker(layer.getBounds().getCenter(), {
          icon: L.divIcon({
            iconSize: [100, 20],
          }),
        });
        label.addTo(map); // 将标签添加到地图
        map.on('zoomend', function () {
          let riverZoom = map.getZoom();
          label.setIcon(
            L.divIcon({
              className: 'lake-label',
              html: riverZoom > 4 ? feature.properties.name : '',
            }),
          );
        });
      },
      style: function () {
        return {
          color: '#9ABAE7',
          opacity: 1,
          weight: 1,
          fillOpacity: 1,
          pane: 'riverPane',
          renderer: canvasRenderer, // 使用 Canvas 渲染
        };
      },
    }).addTo(map);

    // 保存地图实例到全局变量，用于全屏切换时调整大小
    window.mapInstance = map;
    return map;
  };

  var groupColor = ['#377EB8', '#FF7F00', '#E41A1C', '#4DAF4A', '#984EA3'];
  let color_theme = [
    '#0288D1',
    '#FF9800',
    '#F44336',
    '#26A69A',
    '#FFCA28',
    '#26C6DA',
    '#FFEE58',
    '#009688',
    '#8BC34A',
    '#AB47BC',
    '#CDDC39',
    '#FFC107',
    '#E91E63',
    '#9CCC65',
    '#795548',
    '#9C27B0',
    '#3F51B5',
    '#42A5F5',
    '#EF5350',
    '#00BCD4',
    '#66BB6A',
    '#FF5722',
    '#E6EE9C',
    '#3F51B5',
    '#FFEB3B',
    '#D4E157',
    '#673AB7',
    '#4CAF50',
    '#EC407A',
    '#9E9E9E',
  ];

  // 初始化 Species Diversity 图表
  function initSpeciesDiversityCharts() {
    if (taskId.value) {
      initSpeciesDiversityStackedBarChart();
      initSpeciesDiversityPcoaChart();
      // 获取表格数据
      fetchTableData();
      // TreeMap 将在 Swiper 初始化完成后自动初始化
      setTimeout(() => {
        initTreeMap();
      }, 2000); // 给更多时间确保DOM完全渲染
    }
  }

  // Species Diversity 堆积柱形图
  function initSpeciesDiversityStackedBarChart() {
    const chartDom = document.getElementById('chart01');
    if (!chartDom) return;
    const myChart = echarts.init(chartDom);

    getBarPlotData({
      taskId: taskId.value,
      domain: form.value.domain,
      level: form.value.level,
    }).then(response => {
      let data = response.data.data;
      let groups = response.data.groups;
      let legend = response.data.legend;
      let lengthArr = [];
      let legendColor = {};
      legend.forEach((item, index) => {
        legendColor[item] = color_theme[index];
      });
      let xAxisData = [];
      let titleData = [];
      for (let index in groups) {
        let item = data[groups[index]];
        for (let datumKey in item) {
          let arr = [];
          item[datumKey].forEach(x => {
            arr.push({
              value: x[0],
              textStyle: {
                fontSize: 10,
              },
            });
          });
          let obj = {
            type: 'category',
            name: groups[index],
            nameLocation: 'center',
            nameGap: -(
              Number(document.getElementById('chart01').clientHeight) * 0.72
            ),
            nameTextStyle: {
              color: groupColor[index],
              fontSize: '16',
              fontWeight: 'bold',
            },
            data: arr,
            axisTick: { show: true, alignWithLabel: true },
            axisLine: { show: true },
            axisLabel: {
              rotate: 45,
              fontSize: 4,
            },
            gridIndex: index,
          };
          xAxisData.push(obj);
          lengthArr.push(item[datumKey].length);
          break;
        }
      }
      var j = eval(lengthArr.join('+'));
      let left;
      if (j <= 10) {
        // document.getElementById('chart01').style.width = '650px';
        left = 8;
      } else if (j <= 50) {
        // $('#chart01').width('70%');
        // document.getElementById('chart01').style.width = '70%';
        left = 8;
      } else {
        // $('#chart01').width('100%');
        // document.getElementById('chart01').style.width = '100%';
        left = 5;
      }
      let percentage = countPercentage(lengthArr);
      let gridData = [];
      let yAxisData = [];
      let publicStall = left / percentage.length;
      let groupInterval = 0.5;
      for (let i = 0; i < percentage.length; i++) {
        let per = percentage[i] - publicStall - groupInterval;
        let right = 100.2 - left - per;
        let obj = {
          top: '6%',
          bottom: '35%',
          right: right + '%',
          left: left + '%',
        };
        gridData.push(obj);
        yAxisData.push({
          max: 100,
          gridIndex: i,
          name: 'Abundance (%)',
          nameLocation: 'center',
          nameRotate: 90,
          nameGap: 30,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'normal',
          },
          type: 'value',
          splitNumber: 10,
          splitLine: { show: false },
          axisTick: { show: true },
          axisLine: { show: true },
          show: i === 0,
        });
        left = left + per + groupInterval;
      }
      let seriesData = [];
      for (let index in groups) {
        let value = groups[index];
        let item = data[value];
        let i = 0;
        for (let key in item) {
          let data = item[key].map(it => {
            return it[3];
          });
          let obj = {
            name: key,
            type: 'bar',
            stack: value,
            barWidth: data.length <= 10 && groups.length === 2 ? '95%' : '103%',
            data: data,
            itemStyle: {
              color: legendColor[key],
            },
            xAxisIndex: index,
            yAxisIndex: index,
          };
          i++;
          seriesData.push(obj);
        }
      }
      // 添加标题
      // titleData.push({
      //     text: "group",
      //     textStyle: {
      //         fontSize: '14',
      //         fontWeight: 'normal'
      //     },
      //     left: '48.5%',
      //     top: '80%'
      // })
      let legendWidth = 70;
      let legendFontSize = 12;
      if (j <= 10 && legend.length >= 23) {
        legendWidth = 65;
        legendFontSize = 10;
      } else {
        legendWidth = 70;
        legendFontSize = 12;
      }
      let option = {
        grid: gridData,
        legend: {
          top: '83%',
          left: 'center',
          right: 'center',
          data: legend,
          // type: 'scroll',
          orient: 'horizontal',
          pageButtonItemGap: 10,
          formatter: function (name) {
            name = name.length > 10 ? name.substr(0, 7) + '...' : name;
            return [`{a|${name}}`].join('\n');
          },
          textStyle: {
            rich: {
              a: {
                width: legendWidth, // 每个图例的宽度，具体根据字数调整
                fontSize: legendFontSize,
                lineHeight: 6,
              },
            },
          },
          align: 'auto',
          tooltip: {
            show: true,
          },
        },
        tooltip: {
          formatter: function (params) {
            return `${params.marker}&nbsp;${params.seriesName}<br/>
                            Run ID:&nbsp;${params.name}<br/>
                            Abundance:&nbsp;${params.data}<br/>`;
          },
        },
        title: titleData,
        xAxis: xAxisData,
        yAxis: yAxisData,
        series: seriesData,
      };
      myChart.setOption(option);
      window.addEventListener('resize', () => {
        myChart.resize();
      });
    });
  }

  let pcoaUrl = ref(
    `${import.meta.env.VITE_APP_BASE_API}/diversity/pcoa?taskId=demo&domain=B&level=S&betaMethod=bray`,
  );

  // 初始化 Species Diversity PCoA 图表
  function initSpeciesDiversityPcoaChart() {
    if (taskId.value) {
      pcoaUrl.value = `${import.meta.env.VITE_APP_BASE_API}/diversity/pcoa?taskId=${taskId.value}&domain=${form.value.domain}&level=${form.value.level}&betaMethod=bray`;
    }
  }

  // 初始化LDA柱形图 - 单grid完整X轴实现
  function initLDAChart() {
    const chartDom = document.getElementById('ldaChart');
    if (!chartDom) {
      console.error('LDA Chart container not found!');
      return;
    }

    const myChart = echarts.init(chartDom);

    // 按照图片顺序排列所有数据
    const allGroups = [
      { name: 'Candidatus', value: -0.8, group: 'Control' },
      { name: 'Roseburia', value: -1.2, group: 'Control' },
      { name: 'Verrucomicrobiaceae', value: -1.6, group: 'Control' },
      { name: 'Verrucomicrobia', value: -2.0, group: 'Control' },
      { name: 'Akkermansia', value: -2.4, group: 'Control' },
      { name: 'Verrucomicrobiales', value: -2.8, group: 'Control' },
      { name: 'Verrucomicrobiae', value: -3.2, group: 'Control' },
      { name: 'Verrucomicrobiae2', value: -5, group: 'Control' },

      { name: 'Bacteroidales', value: 4.8, group: 'Treatment' },
      { name: 'Bacteroidetes', value: 4.6, group: 'Treatment' },
      { name: 'Bacteroidia', value: 4.4, group: 'Treatment' },
      { name: 'Parabacteroides', value: 4.2, group: 'Treatment' },
      { name: 'Parasutterella', value: 4.0, group: 'Treatment' },
      { name: 'Sutterellaceae', value: 3.8, group: 'Treatment' },
      { name: 'Betaproteobacteria', value: 3.6, group: 'Treatment' },
    ];

    const option = {
      tooltip: {},
      grid: {
        left: '-12%',
        right: '10%',
        bottom: '30%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        textStyle: {
          fontSize: 14,
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
          },
        },
      },
      // 单一Y轴，显示所有物种
      yAxis: {
        type: 'category',
        data: allGroups.map(item => item.name),
        show: false,
      },
      // 单一系列，根据数值正负显示不同颜色
      series: [
        {
          type: 'bar',
          data: allGroups.map(item => ({
            value: item.value,
            label: {
              show: true,
              position: item.group === 'Treatment' ? 'left' : 'right',
              formatter: item.name,
            },
            itemStyle: {
              color: item.group === 'Treatment' ? '#4877C3' : ' #FFBF6C',
            },
          })),
          barWidth: 20,
          itemStyle: {
            borderRadius: 0,
          },
        },
      ],
      // 添加底部X轴标签
      graphic: [
        {
          type: 'text',
          left: 'center',
          bottom: '5%',
          style: {
            text: 'LDA SCORE (log 10)',
            fontSize: 12,
            fill: '#333',
            textAlign: 'center',
          },
        },
      ],
    };

    myChart.setOption(option);

    // 处理窗口大小变化
    window.addEventListener('resize', () => {
      myChart.resize();
    });

    return myChart;
  }

  function initTreeMap() {
    console.log('Initializing TreeMap for groups:', groups.value);

    if (!groups.value || groups.value.length === 0) {
      console.warn('No groups available for TreeMap initialization');
      return;
    }

    for (let group of groups.value) {
      // 注意：DOM中的ID是'Treemap-'（小写m），要保持一致
      const chartDom = document.getElementById(`Treemap-${group}`);
      // 确保容器有尺寸
      if (chartDom.offsetWidth === 0 || chartDom.offsetHeight === 0) {
        console.warn(`TreeMap container has no size for group: ${group}`);
        // 设置默认尺寸
        chartDom.style.width = '100%';
        chartDom.style.height = '400px';
      }

      getTreeMapData({
        taskId: 'demo',
        group: 'GroupA',
      })
        .then(response => {
          if (response && response.data) {
            // 处理二维数组数据
            const rawData = response.data;
            const treeMapData = [];

            // 解析二维数组数据
            // 数据格式: ["GroupA","Alteromonas mediterranea",0.0295532456242498,2.95532456242498]
            // 提取第二列作为label，第四列作为value
            rawData.forEach(row => {
              if (Array.isArray(row) && row.length >= 4) {
                const label = row[1]; // 第二列：物种名称
                const value = parseFloat(row[3]); // 第四列：数值

                if (label && !isNaN(value) && value > 0) {
                  treeMapData.push({
                    name: label,
                    value: value,
                  });
                }
              }
            });

            // 初始化ECharts实例
            const myChart = echarts.init(chartDom);

            // TreeMap配置选项
            const option = {
              tooltip: {
                trigger: 'item',
                formatter: function (params) {
                  return `<strong>${params.name}</strong><br/>Value: ${params.value.toFixed(4)}%`;
                },
              },
              series: [
                {
                  type: 'treemap',
                  data: treeMapData,
                  roam: false,
                  nodeClick: false,
                  breadcrumb: {
                    show: false,
                  },
                  label: {
                    show: true,
                    formatter: function (params) {
                      // 只显示物种名称，如果名称太长则截断
                      const name = params.name;
                      if (name.length > 20) {
                        return name.substring(0, 17) + '...';
                      }
                      return name;
                    },
                    fontSize: 12,
                    color: '#fff',
                    fontWeight: 'bold',
                  },
                  upperLabel: {
                    show: false,
                  },
                  itemStyle: {
                    borderColor: '#fff',
                    borderWidth: 2,
                    gapWidth: 2,
                  },
                  emphasis: {
                    label: {
                      show: true,
                      fontSize: 14,
                      fontWeight: 'bold',
                    },
                    itemStyle: {
                      shadowBlur: 10,
                      shadowColor: 'rgba(0,0,0,0.3)',
                    },
                  },
                },
              ],
              // 使用渐变色彩
              color: [
                '#FF6F61',
                '#FFB347',
                '#FFD966',
                '#A3D39C',
                '#70A1D7',
                '#D57A9B',
                '#C17FC9',
                '#FF9F80',
                '#827397',
                '#E8A87C',
                '#7EB6A3',
                '#FFB1B0',
                '#F5DEB3',
                '#92B6D5',
                '#BC9CFF',
              ],
            };

            // 设置配置项并渲染图表
            myChart.setOption(option);

            // 响应式调整
            window.addEventListener('resize', function () {
              myChart.resize();
            });
          }
        })
        .catch(error => {
          console.error(
            `Failed to load TreeMap data for group ${group}:`,
            error,
          );
        });
    }
  }

  function handleAnalysisTypeChange() {
    nextTick(() => {
      if (form.value.analysisType === 'Species Diversity') {
        barInstance = echarts.init(barRef.value);
        ldaInstance = echarts.init(ldaRef.value);
        barInstance?.resize();
        ldaInstance?.resize();
      }
    });
  }

  // 处理购物车选择
  const handleCartSelection = data => {
    selectedCartCount.value = data.count;
  };

  onMounted(async () => {
    await nextTick();

    // 加载水体类型选项
    loadWaterBodyTypeOptions();

    // 先获取地理数据，然后初始化地图
    const success = await fetchGeoData();
    if (success) {
      initMap('diversityMap');
    } else {
      console.error('地理数据加载失败，无法初始化地图');
    }

    // 根据初始选择的分析类型初始化相应视图
    initLDAChart();
  });
</script>

<style lang="scss" scoped>
  .container-fluid {
    max-width: 1640px !important;
  }

  .submit-page {
    padding: 120px 0 45px 0;
  }

  :deep(.el-slider__bar),
  .filter-search {
    background-color: #1e7cb2;
  }

  :deep(.el-tabs__item) {
    font-size: 16px;
    font-weight: 600;

    &.is-active {
      color: #0080b0;
    }
  }

  :deep(.el-slider__button) {
    width: 12px;
    height: 12px;
  }

  h3 {
    display: flex;
    align-items: center;
    color: #1e7cb2;
  }

  .filter-svg {
    width: 18px;
    height: 18px;
  }

  :deep(.el-popper.is-dark) {
    width: 300px;
  }

  .svg {
    width: 14px;
    height: 14px;
    margin-right: 0.3rem;
  }

  .filter {
    width: 24px;
    height: 26px;
    margin-right: 0.5rem;
  }

  :deep(.el-form-item__label) {
    font-weight: 600;
  }

  :deep(.leaflet-marker-icon.ocean-label) {
    color: #ffffff;
    font-size: 15px;
    width: 200px !important;
    z-index: 200;
  }

  .lake-label {
    color: #2668b4;
    font-family: initial;
  }

  :deep(.el-upload-dragger) {
    padding: 10px 0;
  }

  :deep(.el-upload-dragger .el-icon--upload) {
    color: #2668b4;
    font-size: 45px;
    margin-bottom: 0;
  }

  :deep(.el-upload-list) {
    margin: 0 !important;
  }

  :deep(.el-button--small.is-round) {
    width: 24px;
    height: 24px;
    font-size: 14px;
    padding: 0;
  }

  .legend {
    //position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
  }

  .cricle {
    background-color: #c6a5f4;
    border-radius: 50%;
  }

  .legend-item {
    display: flex;
    align-items: center;
    justify-content: space-between;

    & > div {
      margin-right: 18px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }

  h4 {
    text-align: center;
    font-size: 1.1rem;
    margin-right: 20px;
  }

  .lake-label {
    color: #2668b4;
    font-family: initial;
  }

  :deep(.lake-label) {
    color: #2668b4;
    font-family: initial;
    white-space: nowrap;
  }

  :deep(.leaflet-div-icon) {
    background: transparent;
    border: none;
  }

  /* New styles for the updated form */
  .species-section {
    padding: 0 0 0 16px;
  }

  .species-warning {
    color: #f56c6c;
    font-size: 12px;
    margin-top: 4px;
    line-height: 1.4;
  }

  .data-filter-section {
    padding: 0 4px 0 16px;
    flex: 1;
  }

  .diversity-group {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 16px;
    background-color: #ffffff;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .group-header {
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 8px;
    margin-bottom: 16px;
  }

  .group-title {
    color: #1e7cb2;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
  }

  .group-actions {
    display: flex;
  }

  .group-content {
    .el-form-item {
      margin-bottom: 12px;
    }
  }

  .upload-section {
    flex: 1;

    .upload-demo {
      margin-top: 8px;
    }
  }

  .mb-2 {
    margin-bottom: 12px !important;
  }

  .mb-3 {
    margin-bottom: 20px !important;
  }

  :deep(.el-radio-group) {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
  }

  :deep(.el-radio) {
    margin-right: 0;
  }

  /* Section label styling */
  .section-label {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: #1e7cb2;
    padding-bottom: 4px;

    .el-icon {
      margin-right: 0.2rem;
    }
  }

  /* Custom radio button styling */
  .custom-radio-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    :deep(.el-radio-button) {
      margin-right: 0;
    }

    :deep(.el-radio-button__inner) {
      background-color: #f5f5f5;
      border: 1px solid #dcdfe6;
      color: #606266;
      padding: 4px 10px !important;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #3498db;
        border-color: #3498db;
        color: #ffffff;
      }
    }

    :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
      background-color: #3498db;
      border-color: #3498db;
      color: #ffffff;
      box-shadow: none;
    }

    :deep(.el-radio-button:first-child .el-radio-button__inner) {
      border-radius: 4px;
    }

    :deep(.el-radio-button:last-child .el-radio-button__inner) {
      border-radius: 4px;
    }
  }

  /* Group title input styling */
  .group-title-input {
    :deep(.el-input__wrapper) {
      background-color: transparent;
      border: none;
      box-shadow: none;
      padding: 0;
    }

    :deep(.el-input__inner) {
      color: #1e7cb2;
      font-size: 16px;
      font-weight: 600;
      text-align: left;
    }
  }

  /* Metadata popover styling */
  :deep(.metadata-popover) {
    .metadata-selector {
      .metadata-title {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        text-align: center;
      }

      .column-checkboxes {
        max-height: 300px;
        overflow-y: auto;
        margin-bottom: 16px;

        .column-checkbox {
          display: block;
          margin-bottom: 8px;

          :deep(.el-checkbox__label) {
            font-size: 14px;
            color: #606266;
          }
        }
      }

      .metadata-actions {
        text-align: center;
        border-top: 1px solid #e4e7ed;
        padding-top: 12px;
      }
    }
  }

  /* 购物车水平布局样式 */
  .cart-form-item {
    display: flex;
    align-items: center;
    gap: 12px;

    .cart-label {
      font-size: 14px;
      font-weight: 700;
      color: #606266;
      white-space: nowrap;
      min-width: fit-content;
    }

    .cart-section {
      flex: 1;
    }
  }

  /* 选中数据信息样式 */
  .selected-data-info {
    margin-top: 8px;
    padding-left: 0;

    .el-tag {
      font-size: 12px;
      padding: 4px 8px;
    }
  }

  /* 分组标签样式 */
  .group-tag {
    font-weight: 600;
    border-radius: 4px;
    font-size: 12px;
    padding: 2px 8px;
  }

  /* 轮播图样式 */
  .chart-swiper-container {
    width: 100%;
    position: relative;

    .chart-swiper {
      width: 100%;
      height: 400px;
      border-radius: 8px;
      overflow: hidden;

      .swiper-slide-content {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        .chart-image {
          width: 100%;
          height: 100%;
          object-fit: contain;
          border-radius: 4px;
        }
      }

      // 导航按钮样式
      :deep(.swiper-button-next),
      :deep(.swiper-button-prev) {
        color: #3498db;
        background: rgba(255, 255, 255, 0.9);
        width: 40px;
        height: 40px;
        border-radius: 50%;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 1);
          transform: scale(1.1);
        }

        &::after {
          font-size: 16px;
          font-weight: 600;
        }
      }

      // 分页器样式
      :deep(.swiper-pagination) {
        bottom: 15px;

        .swiper-pagination-bullet {
          background: rgba(255, 255, 255, 0.7);
          opacity: 1;
          width: 10px;
          height: 10px;
          margin: 0 4px;
          transition: all 0.3s ease;

          &.swiper-pagination-bullet-active {
            background: #3498db;
            transform: scale(1.2);
          }
        }
      }
    }
  }

  /* map */
  .chart-card {
    position: absolute;
    right: 10px;
    bottom: 10px;
    color: #272728;
    background: rgba(255, 255, 255, 0.9);
    border-left: 1px solid #007ed31a;
    width: auto;
    min-width: 280px;
    padding: 12px;
    border-radius: 8px;
    z-index: 999;
    font-size: 14px;

    // 样本信息样式
    .sample-info {
      margin-bottom: 4px;

      .sample-count {
        color: #0080b0;
        font-weight: 600;
        margin-left: 4px;
      }
    }

    // 图例容器样式
    .legend-container {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      gap: 8px;
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px dashed #999;
    }

    // 图例项样式
    .legend-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;

      .size-label {
        text-align: center;
        font-weight: 500;
        white-space: nowrap;
        line-height: 1;
      }

      .circle {
        background: #c3aaf1;
        border-radius: 50%;
      }
    }

    h4 {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }

  .fullscreen-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
  }

  .no-results-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 560px;
    background-color: #fffff5;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
  }

  .no-results-message {
    text-align: center;
    color: #666;
    font-size: 16px;
  }
</style>
