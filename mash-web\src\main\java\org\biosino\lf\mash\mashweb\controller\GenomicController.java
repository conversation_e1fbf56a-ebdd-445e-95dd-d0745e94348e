package org.biosino.lf.mash.mashweb.controller;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.mash.mashweb.core.page.TableDataInfo;
import org.biosino.lf.mash.mashweb.core.web.AjaxResult;
import org.biosino.lf.mash.mashweb.dto.*;
import org.biosino.lf.mash.mashweb.service.CacheService;
import org.biosino.lf.mash.mashweb.service.GenomicService;
import org.biosino.lf.mash.mashweb.util.kegg.KeggEntry;
import org.biosino.lf.mash.mashweb.vo.SamplesBioGeographyVO;
import org.biosino.lf.mash.mashweb.vo.SelectItemVO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Li
 * @date 2025/7/21
 */
@RestController
@RequestMapping("/genomic")
@RequiredArgsConstructor
public class GenomicController extends BaseController {
    private final GenomicService genomicService;


    @RequestMapping("/biogeography")
    public AjaxResult createBiogeography(@RequestBody BiogeographyCreateDTO paramsDTO) {
        String id = genomicService.createBiogeography(paramsDTO);
        return AjaxResult.success(id);
    }

    @RequestMapping("/functionAnalysis")
    public AjaxResult createFunctionAnalysis(@RequestBody FunctionCreateDTO paramsDTO) {
        String id = genomicService.createFunctionAnalysis(paramsDTO);
        return AjaxResult.success(id);
    }

    @RequestMapping("/getKoList")
    public AjaxResult getKoList(@RequestBody BiogeographyResultQueryDTO queryDTO) {
        List<String> list = genomicService.getKoList(queryDTO);
        return AjaxResult.success(list);
    }

    @RequestMapping("/getTableResult")
    public TableDataInfo getTableResult(@RequestBody BiogeographyResultQueryDTO queryDTO) {
        Page<SamplesBioGeographyVO> result = genomicService.getTableResult(queryDTO);
        return new TableDataInfo(result.getContent(), (int) result.getTotalElements());
    }

    @RequestMapping("/getPathKoToNameList")
    public AjaxResult getPathKoToNameList(SelectQueryDTO query) {
        Map<String, String> pathKoPathNameMap = CacheService.pathKoPathNameMap;
        List<SelectItemVO> allList = new ArrayList<>();
        pathKoPathNameMap.forEach((k, v) -> {
            SelectItemVO itemVO = new SelectItemVO();
            itemVO.setValue(k);
            itemVO.setLabel(v);
            allList.add(itemVO);
        });

        // 搜索过滤
        List<SelectItemVO> filteredList = allList;
        if (StrUtil.isNotBlank(query.getSearch())) {
            filteredList = allList.stream()
                .filter(x -> StrUtil.containsAnyIgnoreCase(x.getLabel(), query.getSearch()) ||
                           StrUtil.containsAnyIgnoreCase(x.getValue(), query.getSearch()))
                .toList();
        }

        // 实现分页逻辑
        int pageNum = query.getPageNum() != null ? query.getPageNum() : 0;
        int pageSize = query.getPageSize() != null ? query.getPageSize() : 50;
        int startIndex = pageNum * pageSize;
        int endIndex = Math.min(startIndex + pageSize, filteredList.size());

        // 如果起始索引超出范围，返回空列表
        if (startIndex >= filteredList.size()) {
            return AjaxResult.success(new ArrayList<>());
        }

        // 获取当前页的数据
        List<SelectItemVO> pagedList = filteredList.subList(startIndex, endIndex);

        return AjaxResult.success(pagedList);
    }

    @RequestMapping("/getPathwayName")
    public AjaxResult getPathway(SelectQueryDTO query) {
        Map<String, List<KeggEntry>> pathToKeggEntryMap = CacheService.pathToKeggEntryMap;
        List<SelectItemVO> allList = new ArrayList<>();
        pathToKeggEntryMap.forEach((x, y) -> {
            KeggEntry keggEntry = y.get(0);
            String pathwayName = keggEntry.getPathwayName();
            SelectItemVO itemVO = new SelectItemVO();
            itemVO.setValue(x);
            itemVO.setLabel(pathwayName);
            allList.add(itemVO);
        });

        // 搜索过滤
        List<SelectItemVO> filteredList = allList;
        if (StrUtil.isNotBlank(query.getSearch())) {
            filteredList = allList.stream()
                .filter(x -> StrUtil.containsAnyIgnoreCase(x.getLabel(), query.getSearch()) ||
                           StrUtil.containsAnyIgnoreCase(x.getValue(), query.getSearch()))
                .toList();
        }

        // 实现分页逻辑
        int pageNum = query.getPageNum() != null ? query.getPageNum() : 0;
        int pageSize = query.getPageSize() != null ? query.getPageSize() : 50;
        int startIndex = pageNum * pageSize;
        int endIndex = Math.min(startIndex + pageSize, filteredList.size());

        // 如果起始索引超出范围，返回空列表
        if (startIndex >= filteredList.size()) {
            return AjaxResult.success(new ArrayList<>());
        }

        // 获取当前页的数据
        List<SelectItemVO> pagedList = filteredList.subList(startIndex, endIndex);

        return AjaxResult.success(pagedList);
    }

    @RequestMapping("/getResultMapData")
    public AjaxResult getResultMapData(@RequestBody BiogeographyResultQueryDTO queryDTO) {
        MapDataInfoDTO result = genomicService.getMapData(queryDTO);
        return AjaxResult.success(result);
    }

    @RequestMapping("/heatmapData")
    public AjaxResult heatmapData(@RequestBody BiogeographyResultQueryDTO queryDTO) {
        Object result = genomicService.heatmapData(queryDTO.getTaskId(), queryDTO.getType());
        return AjaxResult.success(result);
    }

    @RequestMapping("/koGenePathwayTable")
    public AjaxResult koGenePathwayTable(@RequestBody BiogeographyResultQueryDTO queryDTO) {
        List<KoGenePathwayDTO> result = genomicService.koGenePathwayTable(queryDTO.getTaskId());
        return AjaxResult.success(result);
    }

}
